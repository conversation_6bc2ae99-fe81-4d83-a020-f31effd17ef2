# 搬运工逻辑删除功能 - Apifox 测试用例

## 功能概述
将搬运工删除方法从物理删除改为逻辑删除，通过设置 `isDisabled` 字段为 `true` 来标记删除状态。

## 修改内容
1. **UnloadingWorker.java** - 添加 `isDisabled` 字段
2. **UnloadingWorkerService.java** - 修改 `deleteWorker` 方法实现逻辑删除

## Apifox 测试用例

### 测试用例 1：正常删除搬运工

**接口信息：**
- **请求方式：** POST
- **请求URL：** `{{baseUrl}}/unloading/worker/delete/{id}`
- **接口描述：** 根据id删除搬运工（逻辑删除）

**请求参数：**
- **路径参数：**
  - `id` (Long): 搬运工ID，示例值：`1`

**请求头：**
```
Authorization: {{authorization}}
Content-Type: application/json
```

**前置条件：**
1. 搬运工ID存在且未被删除
2. 用户已登录并有删除权限

**测试步骤：**
1. 发送删除请求
2. 验证响应状态码
3. 查询数据库验证逻辑删除结果

**预期结果：**
- **响应状态码：** 200
- **响应体：** 无内容（void）
- **数据库验证：**
  - 搬运工记录的 `is_disabled` 字段被设置为 `1`
  - `modified_time` 被更新
  - 记录仍存在于数据库中

**测试脚本（后置操作）：**
```javascript
// 验证响应状态码
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

// 验证响应时间
pm.test("Response time is less than 2000ms", function () {
    pm.expect(pm.response.responseTime).to.be.below(2000);
});

// 保存被删除的搬运工ID用于后续测试
pm.environment.set("deletedWorkerId", pm.request.url.path[pm.request.url.path.length - 1]);
```

### 测试用例 2：删除不存在的搬运工

**接口信息：**
- **请求方式：** POST
- **请求URL：** `{{baseUrl}}/unloading/worker/delete/{id}`

**请求参数：**
- **路径参数：**
  - `id` (Long): 不存在的搬运工ID，示例值：`99999`

**请求头：**
```
Authorization: {{authorization}}
Content-Type: application/json
```

**预期结果：**
- **响应状态码：** 500 或 400
- **响应体：** 包含错误信息 "搬运工不存在"

**测试脚本：**
```javascript
pm.test("Status code is 500 or 400", function () {
    pm.expect(pm.response.code).to.be.oneOf([400, 500]);
});

pm.test("Error message contains '搬运工不存在'", function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson.message || responseJson.msg).to.include("搬运工不存在");
});
```

### 测试用例 3：删除无效ID的搬运工

**接口信息：**
- **请求方式：** POST
- **请求URL：** `{{baseUrl}}/unloading/worker/delete/{id}`

**请求参数：**
- **路径参数：**
  - `id` (Long): 无效ID，示例值：`0` 或 `-1`

**预期结果：**
- **响应状态码：** 500 或 400
- **响应体：** 包含参数无效的错误信息

**测试脚本：**
```javascript
pm.test("Status code is 500 or 400", function () {
    pm.expect(pm.response.code).to.be.oneOf([400, 500]);
});

pm.test("Response contains error message", function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson.message || responseJson.msg).to.exist;
});
```

### 测试用例 4：验证逻辑删除后的查询行为

**前置条件：**
1. 先执行测试用例1删除一个搬运工
2. 记录被删除搬运工的ID

**测试步骤：**
1. 调用搬运工列表查询接口
2. 验证被逻辑删除的搬运工不在列表中

**接口信息：**
- **请求方式：** POST
- **请求URL：** `{{baseUrl}}/unloading/worker/list`

**请求体：**
```json
{
    "page": false
}
```

**预期结果：**
- 被逻辑删除的搬运工不出现在查询结果中
- 其他正常搬运工正常显示

**测试脚本：**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Deleted worker should not appear in list", function () {
    const responseJson = pm.response.json();
    const deletedWorkerId = pm.environment.get("deletedWorkerId");
    
    if (deletedWorkerId && Array.isArray(responseJson)) {
        const foundWorker = responseJson.find(worker => worker.id == deletedWorkerId);
        pm.expect(foundWorker).to.be.undefined;
    }
});
```

### 测试用例 5：验证搬运工设备权限清理

**前置条件：**
1. 搬运工拥有设备权限
2. 记录搬运工ID

**测试步骤：**
1. 删除搬运工
2. 查询该搬运工的设备权限

**验证接口：**
- **请求方式：** POST
- **请求URL：** `{{baseUrl}}/unloading/worker/access/get/{workerId}`

**预期结果：**
- 搬运工的设备权限被清空
- 返回空数组

**测试脚本：**
```javascript
pm.test("Status code is 200", function () {
    pm.response.to.have.status(200);
});

pm.test("Worker access should be empty", function () {
    const responseJson = pm.response.json();
    pm.expect(responseJson).to.be.an('array').that.is.empty;
});
```

### 测试用例 6：重复删除已删除的搬运工

**前置条件：**
1. 搬运工已被逻辑删除

**测试步骤：**
1. 再次删除同一个搬运工
2. 验证系统行为

**预期结果：**
- **响应状态码：** 500 或 400
- **响应体：** 包含 "搬运工不存在" 错误信息

## 环境变量设置

在 Apifox 环境中设置以下变量：

```json
{
    "baseUrl": "http://localhost:8080",
    "authorization": "Bearer your_token_here",
    "testWorkerId": "1",
    "deletedWorkerId": ""
}
```

## 数据库验证 SQL

测试完成后可以通过以下SQL验证结果：

```sql
-- 查看逻辑删除的搬运工
SELECT id, worker_no, name, contact, is_disabled, modified_time 
FROM px_unloading_worker 
WHERE is_disabled = 1;

-- 查看正常的搬运工（应该不包含被删除的）
SELECT id, worker_no, name, contact, is_disabled 
FROM px_unloading_worker 
WHERE is_disabled = 0;

-- 验证特定搬运工的删除状态
SELECT id, worker_no, name, contact, is_disabled, modified_time 
FROM px_unloading_worker 
WHERE id = {被删除的搬运工ID};
```

## 测试集合配置

### 前置脚本（Collection Pre-request Script）
```javascript
// 设置基础URL和认证信息
pm.environment.set("baseUrl", "http://localhost:8080");
// 确保设置了正确的authorization token
```

### 后置脚本（Collection Tests）
```javascript
// 全局响应时间检查
pm.test("Response time is reasonable", function () {
    pm.expect(pm.response.responseTime).to.be.below(5000);
});
```

## 注意事项

1. **权限验证：** 确保测试用户有删除搬运工的权限
2. **数据准备：** 测试前需要准备测试数据（创建搬运工）
3. **数据清理：** 测试完成后可以通过物理删除清理测试数据
4. **并发测试：** 可以添加并发删除同一搬运工的测试用例
5. **业务逻辑：** 确保被删除的搬运工不会出现在业务流程中（如订单分配等）

## 测试执行顺序

建议按以下顺序执行测试用例：
1. 测试用例 3：删除无效ID的搬运工
2. 测试用例 2：删除不存在的搬运工  
3. 测试用例 1：正常删除搬运工
4. 测试用例 4：验证逻辑删除后的查询行为
5. 测试用例 5：验证搬运工设备权限清理
6. 测试用例 6：重复删除已删除的搬运工
