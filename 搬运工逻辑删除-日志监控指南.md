# 搬运工逻辑删除功能 - 日志监控指南

## 功能概述
将搬运工删除方法从物理删除改为逻辑删除，通过设置 `isDisabled` 字段为 `true` 来标记删除状态。

### 字段含义说明
- **`is_disabled = 0`**：未删除/正常状态
- **`is_disabled = 1`**：已删除/禁用状态  
- **`worker.setIsDisabled(Boolean.TRUE)`**：设置为已删除状态（数据库存储为1）

## 修改内容
1. **UnloadingWorker.java** - 添加 `isDisabled` 字段
2. **UnloadingWorkerService.java** - 修改 `deleteWorker` 方法实现逻辑删除，添加详细日志
3. **UnloadingWorkerController.java** - 添加详细日志
4. **UnloadingWorkerMapper.xml** - 修复所有查询语句，确保过滤逻辑删除的数据

## 关键日志标识

### 删除操作日志
```
【搬运工删除】开始删除搬运工，ID: {id}
【搬运工逻辑删除】开始删除搬运工，ID: {id}
【搬运工逻辑删除】查询搬运工结果，ID: {id}, 是否存在: {exists}, 当前isDisabled状态: {status}
【搬运工逻辑删除】开始清除搬运工设备权限，ID: {id}
【搬运工逻辑删除】设备权限清除完成，ID: {id}
【搬运工逻辑删除】开始设置逻辑删除标记，ID: {id}, 原isDisabled: {oldStatus}
【搬运工逻辑删除】逻辑删除完成，ID: {id}, 更新结果: {result}, 新isDisabled: {newStatus}
【搬运工逻辑删除】搬运工逻辑删除成功，ID: {id}
【搬运工删除】搬运工删除成功，ID: {id}
```

### 查询操作日志
```
【搬运工查询】开始查询搬运工，ID: {id}
【搬运工查询】Service层开始查询搬运工，ID: {id}
【搬运工查询】Service层查询结果，ID: {id}, 是否找到: {found}, isDisabled: {status}, workerNo: {workerNo}
【搬运工查询】查询结果，ID: {id}, 是否找到: {found}, isDisabled: {status}

【搬运工列表查询】开始查询搬运工列表，查询条件: {searchVo}
【搬运工列表】Service层开始查询搬运工列表，查询条件: {searchVo}
【搬运工列表】Service层查询完成，返回数量: {count}
【搬运工列表】搬运工详情 - ID: {id}, workerNo: {workerNo}, name: {name}, isDisabled: {status}, status: {workStatus}
【搬运工列表查询】查询完成，返回数量: {count}
【搬运工列表查询】前3个搬运工ID和状态: {workerInfo}

【搬运工分页查询】开始分页查询，页码: {pageNo}, 页大小: {pageSize}, 查询条件: {searchVo}
【搬运工分页】Service层开始分页查询，页码: {pageNo}, 页大小: {pageSize}
【搬运工统计】开始统计搬运工数量，查询条件: {searchVo}
【搬运工统计】统计完成，符合条件的搬运工数量: {count}
【搬运工分页】Service层分页查询完成，总数: {total}, 当前页数据量: {currentPageSize}
【搬运工分页查询】查询完成，总数: {total}, 当前页数据量: {currentPageSize}
```

### Mapper层SQL日志
```
DEBUG com.senox.tms.mapper.UnloadingWorkerMapper.listWorker - ==>  Preparing: SELECT ... FROM px_unloading_worker WHERE ... AND is_disabled = 0
DEBUG com.senox.tms.mapper.UnloadingWorkerMapper.listWorker - ==> Parameters: ...
DEBUG com.senox.tms.mapper.UnloadingWorkerMapper.listWorker - <==      Total: {count}

DEBUG com.senox.tms.mapper.UnloadingWorkerMapper.countWorker - ==>  Preparing: SELECT COUNT(*) FROM px_unloading_worker WHERE ... AND is_disabled = 0
DEBUG com.senox.tms.mapper.UnloadingWorkerMapper.countWorker - ==> Parameters: ...
DEBUG com.senox.tms.mapper.UnloadingWorkerMapper.countWorker - <==      Total: 1
```

## 测试步骤和日志验证

### 步骤1：删除搬运工
**API调用：** `POST /unloading/worker/delete/{id}`

**期望日志序列：**
1. `【搬运工删除】开始删除搬运工，ID: {id}`
2. `【搬运工逻辑删除】开始删除搬运工，ID: {id}`
3. `【搬运工逻辑删除】查询搬运工结果，ID: {id}, 是否存在: true, 当前isDisabled状态: false`
4. `【搬运工逻辑删除】开始清除搬运工设备权限，ID: {id}`
5. `【搬运工逻辑删除】设备权限清除完成，ID: {id}`
6. `【搬运工逻辑删除】开始设置逻辑删除标记，ID: {id}, 原isDisabled: false`
7. `【搬运工逻辑删除】逻辑删除完成，ID: {id}, 更新结果: true, 新isDisabled: true`
8. `【搬运工逻辑删除】搬运工逻辑删除成功，ID: {id}`
9. `【搬运工删除】搬运工删除成功，ID: {id}`

### 步骤2：验证查询过滤
**API调用：** `POST /unloading/worker/list`

**期望日志序列：**
1. `【搬运工列表查询】开始查询搬运工列表，查询条件: {searchVo}`
2. `【搬运工列表】Service层开始查询搬运工列表，查询条件: {searchVo}`
3. `【搬运工统计】开始统计搬运工数量，查询条件: {searchVo}`
4. SQL日志显示：`AND is_disabled = 0`
5. `【搬运工统计】统计完成，符合条件的搬运工数量: {count}`
6. `【搬运工列表】Service层查询完成，返回数量: {count}`
7. 确认被删除的搬运工不在返回结果中

### 步骤3：尝试查询已删除的搬运工
**API调用：** `GET /unloading/worker/get/{deletedId}`

**期望日志序列：**
1. `【搬运工查询】开始查询搬运工，ID: {deletedId}`
2. `【搬运工查询】Service层开始查询搬运工，ID: {deletedId}`
3. `【搬运工查询】Service层查询结果，ID: {deletedId}, 是否找到: true, isDisabled: true, workerNo: {workerNo}`
4. `【搬运工查询】查询结果，ID: {deletedId}, 是否找到: true, isDisabled: true`

**注意：** `getById` 方法会返回已删除的记录，因为它是直接根据主键查询，不经过 Mapper 的过滤条件。

## 数据库验证SQL

```sql
-- 验证逻辑删除状态
SELECT id, worker_no, name, contact, is_disabled, modified_time 
FROM px_unloading_worker 
WHERE id = {被删除的搬运工ID};

-- 验证查询过滤效果
SELECT COUNT(*) as total_count FROM px_unloading_worker;
SELECT COUNT(*) as active_count FROM px_unloading_worker WHERE is_disabled = 0;
SELECT COUNT(*) as deleted_count FROM px_unloading_worker WHERE is_disabled = 1;
```

## 常见问题排查

### 问题1：删除后仍能在列表中看到
**排查步骤：**
1. 检查日志中的 `更新结果: true`
2. 检查SQL日志是否包含 `AND is_disabled = 0`
3. 直接查询数据库验证 `is_disabled` 字段值

### 问题2：删除操作失败
**排查步骤：**
1. 查看是否有 `【搬运工逻辑删除】搬运工不存在，ID: {id}` 日志
2. 查看是否有 `【搬运工逻辑删除】更新失败，ID: {id}` 日志
3. 检查数据库连接和事务状态

### 问题3：设备权限未清理
**排查步骤：**
1. 查看 `【搬运工逻辑删除】设备权限清除完成，ID: {id}` 日志
2. 调用设备权限查询接口验证
3. 检查 `UnloadingWorkerAccessService.addWorkerAccess` 方法

## 部署后验证清单

- [ ] 确认日志级别配置正确（`com.senox.tms.mapper: debug`）
- [ ] 执行删除操作，检查完整日志序列
- [ ] 验证查询接口不返回已删除数据
- [ ] 确认数据库中 `is_disabled` 字段正确更新
- [ ] 验证设备权限被正确清理
- [ ] 测试异常情况（删除不存在的ID等）

## 性能监控

关注以下性能指标：
- 删除操作响应时间
- 查询操作响应时间
- 数据库连接池状态
- 事务执行时间

通过这些详细的日志，您可以在服务器上完整地跟踪逻辑删除功能的执行过程，确保功能正常工作。
