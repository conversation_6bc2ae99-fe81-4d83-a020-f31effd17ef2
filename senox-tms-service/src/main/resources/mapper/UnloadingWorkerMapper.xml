<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.senox.tms.mapper.UnloadingWorkerMapper">

    <select id="getMaxOrderNum" resultType="int">
        SELECT MAX(order_num) FROM px_unloading_worker WHERE is_disabled = 0
    </select>

    <select id="filterListWorker" resultType="com.senox.tms.domain.UnloadingWorker">
        SELECT id, worker_no, name, status, order_num FROM px_unloading_worker
        WHERE order_num >= #{orderNumStart} and order_num <![CDATA[<=]]> #{orderNumEnd} AND is_disabled = 0
    </select>

    <select id="findMaxWorkerSign" parameterType="java.lang.String" resultType="java.lang.String">
        SELECT MAX(worker_sign) FROM px_unloading_worker WHERE worker_sign LIKE CONCAT(#{prefix}, '%') AND is_disabled = 0;
    </select>

    <select id="countWorker" parameterType="com.senox.tms.vo.UnloadingWorkerSearchVo" resultType="int">
        SELECT COUNT(*) FROM px_unloading_worker
        <where>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name like concat('%', #{keyword}, '%') OR contact like concat('%', #{keyword}, '%'))
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            AND is_disabled = 0
        </where>
    </select>

    <select id="listWorker" parameterType="com.senox.tms.vo.UnloadingWorkerSearchVo" resultType="com.senox.tms.domain.UnloadingWorker">
        SELECT
              id
            , worker_no
            , worker_sign
            , name
            , born_date
            , contact
            , face_url
            , status
            , order_num
        FROM px_unloading_worker
        <where>
            <if test="workerNo != null and workerNo != ''">
                AND worker_no LIKE CONCAT('%', #{workerNo}, '%')
            </if>
            <if test="keyword != null and keyword != ''">
                AND (name like concat('%', #{keyword}, '%') OR contact like concat('%', #{keyword}, '%'))
            </if>
            <if test="statusList != null and statusList.size() > 0">
                and status in
                <foreach collection="statusList" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            AND is_disabled = 0
        </where>
        <choose>
            <when test="orderStr != null and orderStr != ''">
                ORDER BY ${orderStr}
            </when>
            <otherwise>
                ORDER BY
                    CASE status
                        WHEN 1 THEN 1
                        WHEN 2 THEN 2
                        WHEN 0 THEN 3
                        END ASC
                    , CASE order_num
                        WHEN order_num = 0 THEN 9999
                        ELSE order_num END ASC
            </otherwise>
        </choose>
        <if test="page">
            LIMIT ${offset}, ${pageSize}
        </if>
    </select>

</mapper>
