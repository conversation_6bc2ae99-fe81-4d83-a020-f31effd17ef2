package com.senox.tms.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.senox.common.constant.ResultConst;
import com.senox.common.exception.BusinessException;
import com.senox.common.exception.InvalidParameterException;
import com.senox.common.utils.*;
import com.senox.common.vo.PageResult;
import com.senox.tms.config.AppConfig;
import com.senox.tms.constant.TmsConst;
import com.senox.tms.constant.UnloadingWorkerStatus;
import com.senox.tms.domain.UnloadingAttendance;
import com.senox.tms.domain.UnloadingWorker;
import com.senox.tms.domain.UnloadingWorkerAccess;
import com.senox.tms.event.UnloadingWebSocketEvent;
import com.senox.tms.mapper.UnloadingWorkerMapper;
import com.senox.tms.utils.ContextUtils;
import com.senox.tms.vo.UnloadingWorkerFaceUrlVo;
import com.senox.tms.vo.UnloadingWorkerSearchVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/13 13:40
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UnloadingWorkerService extends ServiceImpl<UnloadingWorkerMapper, UnloadingWorker> {

    private final UnloadingAttendanceService attendanceService;
    private final AppConfig appConfig;
    private final UnloadingWorkerAccessService workerAccessService;
    private final ApplicationEventPublisher publisher;

    /**
     * 新增搬运工
     * @param worker
     */
    public void addWorker(UnloadingWorker worker) {
        if (StringUtils.isBlank(worker.getWorkerSign())) {
            log.info("【装卸搬运工标识】---搬运工标识开始生成---");
            prepareWorkerSign(worker);
        }
        worker.setStatus(UnloadingWorkerStatus.NOT_LISTED.getNumber());
        worker.setOrderNum(0);
        worker.setCreateTime(LocalDateTime.now());
        worker.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityCreator(worker);
        ContextUtils.initEntityModifier(worker);
        save(worker);
    }

    /**
     * 更新搬运工信息，不更新状态和人脸
     * @param worker
     */
    public void updateWorker(UnloadingWorker worker) {
        if (!WrapperClassUtils.biggerThanLong(worker.getId(), 0L)) {
            throw new InvalidParameterException();
        }
        ContextUtils.initEntityModifier(worker);
        worker.setModifiedTime(LocalDateTime.now());
        worker.setOrderNum(null);
        worker.setStatus(null);
        worker.setFaceUrl(null);
        worker.setWorkerSign(null);
        updateById(worker);
    }

    /**
     * 保存搬运工
     * @param worker
     */
    public void saveWorker(UnloadingWorker worker) {
        if (!WrapperClassUtils.biggerThanLong(worker.getId(), 0L)) {
            addWorker(worker);
        } else {
            updateWorker(worker);
        }
    }

    /**
     * 批量更新搬运工状态
     * @param ids
     * @param status
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchWorkerStatus(List<Long> ids, Integer status) {
        for (Long id : ids) {
            updateWorkerStatus(id, status);
        }
        publisher.publishEvent(new UnloadingWebSocketEvent(this));
    }

    /**
     * 更新搬运工状态
     * @param id
     * @param status
     */
    public void updateWorkerStatus(Long id, Integer status) {
        UnloadingWorker dbWorker = findById(id);
        if (dbWorker == null) {
            log.info("【搬运工】---搬运工未找到。。id为:{}", id);
            return;
        }
        updateStatus(status, dbWorker);
    }

    /**
     * 根据标识更新搬运工状态
     * @param workerSign
     * @param status
     */
    public void updateWorkerStatus(String workerSign, Integer status) {
        UnloadingWorker dbWorker = findByWorkerSign(workerSign);
        if (dbWorker == null) {
            log.info("【搬运工】---搬运工未找到。。标识为:{}", workerSign);
            return;
        }
        updateStatus(status, dbWorker);
    }

    /**
     * 更新状态
     * @param status
     * @param dbWorker
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateStatus(Integer status, UnloadingWorker dbWorker) {
        if (Objects.equals(status, dbWorker.getStatus())) {
            log.info("原状态 {} 更新的状态 {}， 状态一致， 忽略", dbWorker.getStatus(), status);
            return;
        }
        UnloadingWorker worker = new UnloadingWorker();
        worker.setId(dbWorker.getId());
        worker.setStatus(status);
        if (UnloadingWorkerStatus.NOT_LISTED.getNumber() == status) {
            worker.setOrderNum(0);
        } else if (UnloadingWorkerStatus.ALREADY_LISTED.getNumber() == status) {
            //find max order
            worker.setOrderNum(getBaseMapper().getMaxOrderNum() + 2);
        }
        ContextUtils.initEntityModifier(worker);
        worker.setModifiedTime(LocalDateTime.now());
        if (updateById(worker)) {
            UnloadingWorkerStatus workerStatus = UnloadingWorkerStatus.fromStatus(status);
            UnloadingAttendance attendance = new UnloadingAttendance();
            attendance.setWorkerId(worker.getId());
            attendance.setWorkerNo(dbWorker.getWorkerNo());
            attendance.setWorkerName(dbWorker.getName());
            attendance.setRemark(workerStatus == null ? StringUtils.EMPTY : workerStatus.getName());
            attendanceService.addAttendance(attendance);
        }
        publisher.publishEvent(new UnloadingWebSocketEvent(this));
    }

    /**
     * 根据标识查询搬运工
     * @param workerSign
     * @return
     */
    public UnloadingWorker findByWorkerSign(String workerSign) {
        if (StringUtils.isBlank(workerSign)) {
            throw new InvalidParameterException();
        }
        return getOne(new QueryWrapper<UnloadingWorker>().lambda().eq(UnloadingWorker::getWorkerSign, workerSign));
    }

    /**
     * 根据id查询搬运工
     * @param id
     * @return
     */
    public UnloadingWorker findById(Long id) {
        if (!WrapperClassUtils.biggerThanLong(id, 0L)) {
            throw new InvalidParameterException();
        }
        return getById(id);
    }

    /**
     * 删除搬运工（逻辑删除）
     * @param workerId
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteWorker(Long workerId) {
        if (!WrapperClassUtils.biggerThanLong(workerId, 0L)) {
            throw new InvalidParameterException();
        }
        UnloadingWorker dbWorker = getById(workerId);
        if (dbWorker == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND, "搬运工信息未找到");
        }

        // 清空搬运工的设备权限
        workerAccessService.addWorkerAccess(dbWorker, Collections.emptyList());

        // 逻辑删除：设置禁用标记并修改联系方式避免重复
        UnloadingWorker worker = new UnloadingWorker();
        worker.setId(workerId);
        worker.setDisabled(Boolean.TRUE);
        worker.setContact(dbWorker.getContact().concat("_").concat(dbWorker.getId().toString()));
        ContextUtils.initEntityModifier(worker);
        worker.setModifiedTime(LocalDateTime.now());
        updateById(worker);
    }

    /**
     * 只更新人脸
     * @param faceUrlVo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateFaceUrl(UnloadingWorkerFaceUrlVo faceUrlVo) {
        UnloadingWorker worker = new UnloadingWorker();
        worker.setId(faceUrlVo.getId());
        worker.setFaceUrl(faceUrlVo.getFaceUrl());
        ContextUtils.initEntityModifier(worker);
        worker.setModifiedTime(LocalDateTime.now());
        updateById(worker);
        List<UnloadingWorkerAccess> workerAccesses = workerAccessService.accessListByWorkerIds(Collections.singletonList(faceUrlVo.getId()));
        if (!CollectionUtils.isEmpty(workerAccesses)) {
            workerAccessService.accessRight(getById(faceUrlVo.getId()), workerAccesses, Boolean.FALSE);
        }
    }

    /**
     * 重新排序
     * @param id
     * @param targetId
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetOrderNum(Long id, Long targetId) {
        UnloadingWorker dbWorker = findById(id);
        UnloadingWorker targetWorker = findById(targetId);
        if (dbWorker == null || targetWorker == null) {
            throw new BusinessException(ResultConst.ITEM_NOT_FOUND);
        }
        if (dbWorker.getStatus() != UnloadingWorkerStatus.ALREADY_LISTED.getNumber()
                || targetWorker.getStatus() != UnloadingWorkerStatus.ALREADY_LISTED.getNumber()) {
            throw new BusinessException("只能交换已挂牌的顺序！");
        }
        int srcOrderNum = dbWorker.getOrderNum();
        dbWorker.setOrderNum(targetWorker.getOrderNum());
        dbWorker.setModifiedTime(LocalDateTime.now());
        ContextUtils.initEntityModifier(dbWorker);
        updateById(dbWorker);
        updateBatchOrderNum(id, srcOrderNum, targetWorker);
    }

    /**
     * 批量更新排序号
     * @param id
     * @param srcOrderNum
     * @param targetWorker
     */
    private void updateBatchOrderNum(Long id, Integer srcOrderNum, UnloadingWorker targetWorker) {
        Integer targetOrderNum = targetWorker.getOrderNum();
        //取两个排序的最小值和最大值做筛选
        List<UnloadingWorker> workerList = getBaseMapper().filterListWorker(srcOrderNum > targetOrderNum ? targetOrderNum : srcOrderNum
                , srcOrderNum > targetOrderNum ? srcOrderNum : targetOrderNum);
        List<UnloadingWorker> filterWorkerList = workerList.stream().filter(worker -> !Objects.equals(worker.getId(), id)).collect(Collectors.toList());
        filterWorkerList.forEach(worker -> {
            worker.setOrderNum(worker.getOrderNum() + 2);
            worker.setModifiedTime(LocalDateTime.now());
            ContextUtils.initEntityModifier(worker);
            if (Objects.equals(worker.getId(), targetWorker.getId())) {
                worker.setOrderNum(srcOrderNum);
            }
        });
        updateBatchById(filterWorkerList);
    }

    /**
     * 只更新状态
     * @param ids
     * @param status
     */
    public void updateBatchStatus(List<Long> ids, Integer status) {
        update(new UpdateWrapper<UnloadingWorker>().lambda()
                .in(UnloadingWorker::getId, ids)
                .eq(UnloadingWorker::getStatus, status)
                .eq(UnloadingWorker::getModifiedTime, LocalDateTime.now())
                .eq(UnloadingWorker::getModifierId, ContextUtils.getUserInContext().getUserId()));
    }

    /**
     * 搬运工数量
     * @param searchVo
     * @return
     */
    public int countWorker(UnloadingWorkerSearchVo searchVo) {
        return getBaseMapper().countWorker(searchVo);
    }

    /**
     * 搬运工列表
     * @param searchVo
     * @return
     */
    public List<UnloadingWorker> listWorker(UnloadingWorkerSearchVo searchVo) {
        return getBaseMapper().listWorker(searchVo);
    }

    /**
     * 搬运工分页列表
     * @param searchVo
     * @return
     */
    public PageResult<UnloadingWorker> pageWorker(UnloadingWorkerSearchVo searchVo) {
        return PageUtils.commonPageResult(searchVo, () -> countWorker(searchVo), () -> listWorker(searchVo));
    }

    /**
     * 搬运工标识初始化
     * @param worker
     */
    private void prepareWorkerSign(UnloadingWorker worker) {
        String prefix = appConfig.getUnloadingWorkerSignPrefix().concat(DateUtils.formatYearMonth(LocalDate.now(), DateUtils.PATTERN_COMPACT_DATE));
        // get serial from cache
        String cacheKey = String.format(TmsConst.Cache.KEY_UNLOADING_WORKER_SIGN, prefix);
        Long result = RedisUtils.incr(cacheKey);
        RedisUtils.expire(cacheKey, TmsConst.Cache.TTL_2D);

        // compare serial from db and recorrect serial from cache
        Long dbSerial = findMaxWorkerSign(prefix);
        if (result <= dbSerial) {
            result = ++dbSerial;
            RedisUtils.set(cacheKey, result, TmsConst.Cache.TTL_2D);
        }
        String workerSign = prefix.concat(StringUtils.fixLength(String.valueOf(result), appConfig.getUnloadingOrderNoPostfixLength(), appConfig.getFillChar()));
        log.info("装卸搬运工标识初始化完成...{}", workerSign);
        worker.setWorkerSign(workerSign);
    }

    /**
     * 获取最大搬运工标识
     * @param prefix
     * @return
     */
    private Long findMaxWorkerSign(String prefix) {
        String maxWorkerSign = getBaseMapper().findMaxWorkerSign(prefix);
        return (StringUtils.isBlank(maxWorkerSign) ? 0L : NumberUtils.parseLong(maxWorkerSign.substring(prefix.length()), 0L));
    }
}
