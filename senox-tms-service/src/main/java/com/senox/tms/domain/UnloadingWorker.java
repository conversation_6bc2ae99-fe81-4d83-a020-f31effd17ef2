package com.senox.tms.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.senox.common.domain.TableIdEntity;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2024/9/13 11:38
 */
@TableName("px_unloading_worker")
@Getter
@Setter
public class UnloadingWorker extends TableIdEntity {

    /**
     * 搬运工编号
     */
    private String workerNo;

    /**
     * 搬运工标识
     */
    private String workerSign;

    /**
     * 姓名
     */
    private String name;

    /**
     * 出生日期
     */
    private LocalDate bornDate;

    /**
     * 联系方式
     */
    private String contact;

    /**
     * 人脸
     */
    private String faceUrl;

    /**
     * @see com.senox.tms.constant.UnloadingWorkerStatus
     */
    private Integer status;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 是否禁用（逻辑删除标记）
     */
    private Boolean disabled;
}
